# 网络状态管理解决方案

## 问题描述

当前应用为了便于更新，prerender 直接使用线上地址而不是本地静态资源。但这会导致一个问题：如果断网运行程序时会导致无法访问线上地址，出现白屏，无法重试等其他操作。

## 解决方案

### 核心思路

1. **启动时网络检测**：软件启动时通过 `ping` 包检测网络状态
2. **智能页面加载**：
   - 如果在线：加载线上地址
   - 如果离线：加载本地 HTML 页面，显示离线界面
3. **持续监听**：后台持续监听网络状态变化
4. **自动恢复**：网络恢复后自动重新加载线上地址
5. **用户交互**：提供手动重试和重新加载功能

### 技术实现

#### 1. 主进程网络检测 (`src/main/index.ts`)

```typescript
// 网络状态检测函数
async function checkNetworkStatus(): Promise<boolean> {
  try {
    const res = await ping.promise.probe(PING_HOST, {
      timeout: 3,
      extra: ['-c', '1']
    })
    return res.alive
  } catch (error) {
    return false
  }
}

// 启动时检测并决定加载哪个页面
if (isOnline) {
  mainWindow.loadURL(ONLINE_URL)
} else {
  mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
}

// 启动网络状态监听
startNetworkMonitoring()
```

#### 2. IPC 通信接口 (`src/preload/index.ts`)

```typescript
const api = {
  // 网络状态相关API
  getNetworkStatus: () => ipcRenderer.invoke('get-network-status'),
  checkNetworkStatus: () => ipcRenderer.invoke('check-network-status'),
  reloadOnlineUrl: () => ipcRenderer.invoke('reload-online-url'),
  
  // 监听网络状态变化
  onNetworkStatusChanged: (callback: (isOnline: boolean) => void) => {
    ipcRenderer.on('network-status-changed', (_, isOnline) => callback(isOnline))
  }
}
```

#### 3. 渲染进程网络状态管理 (`src/renderer/src/composables/useNetworkStatus.ts`)

```typescript
export function useNetworkStatus() {
  const isMainProcessOnline = ref(false)
  const browserOnline = useOnline() // @vueuse/core
  
  // 综合判断网络状态
  const getOverallNetworkStatus = () => {
    return isMainProcessOnline.value && browserOnline.value
  }
  
  // 监听主进程的网络状态变化
  window.api.onNetworkStatusChanged(handleNetworkStatusChange)
  
  return {
    isMainProcessOnline,
    browserOnline,
    getOverallNetworkStatus,
    checkNetworkStatus,
    reloadOnlineUrl
  }
}
```

#### 4. 离线页面组件 (`src/renderer/src/components/offline.vue`)

- 显示友好的离线提示界面
- 实时显示网络状态信息
- 提供手动重试和重新加载功能
- 自动重试倒计时功能

#### 5. 应用级网络状态监听 (`src/renderer/src/App.vue`)

```typescript
// 监听网络状态变化，自动切换页面
watch(
  () => getOverallNetworkStatus(),
  (isOnline) => {
    if (!isOnline && route.name !== 'Offline') {
      router.push('/offline')
    } else if (isOnline && route.name === 'Offline') {
      router.push('/consumer/home')
    }
  },
  { immediate: true }
)
```

### 功能特性

#### ✅ 已实现功能

1. **启动时网络检测**：应用启动时自动检测网络状态
2. **智能页面加载**：根据网络状态决定加载线上地址或本地页面
3. **持续网络监听**：每5秒检测一次网络状态
4. **自动页面切换**：网络状态变化时自动切换页面
5. **离线友好界面**：提供美观的离线提示页面
6. **手动重试功能**：用户可以手动检测网络状态
7. **自动重试机制**：30秒自动重试一次
8. **网络状态显示**：实时显示主进程和浏览器的网络状态
9. **类型安全**：完整的 TypeScript 类型定义

#### 🔧 配置项

```typescript
// 在线地址配置
const ONLINE_URL = 'http://**************:9027/#/consumer/home'
const PING_HOST = '**************'
const NETWORK_CHECK_INTERVAL = 5000 // 5秒检查一次
```

### 使用方法

#### 1. 在组件中使用网络状态

```vue
<script setup>
import { useNetworkStatus } from '@/composables/useNetworkStatus'

const { 
  isMainProcessOnline, 
  browserOnline, 
  getOverallNetworkStatus,
  checkNetworkStatus,
  reloadOnlineUrl 
} = useNetworkStatus()

// 检查网络状态
const handleCheck = async () => {
  const status = await checkNetworkStatus()
  console.log('网络状态:', status)
}

// 重新加载在线地址
const handleReload = async () => {
  await reloadOnlineUrl()
}
</script>
```

#### 2. 监听网络状态变化

```typescript
onMounted(() => {
  window.api.onNetworkStatusChanged((isOnline) => {
    console.log('网络状态变化:', isOnline)
  })
})
```

### 测试方法

1. **断网测试**：
   - 断开网络连接
   - 启动应用，应该显示离线页面
   - 恢复网络，应该自动跳转到在线页面

2. **运行时断网**：
   - 应用运行时断开网络
   - 应该自动跳转到离线页面
   - 恢复网络后自动跳转回在线页面

3. **手动重试**：
   - 在离线页面点击"重试连接"按钮
   - 检查网络状态是否正确更新

### 优势

1. **用户体验好**：避免白屏，提供友好的离线提示
2. **自动恢复**：网络恢复后自动重新连接
3. **双重检测**：主进程 ping 检测 + 浏览器在线状态检测
4. **可配置**：可以轻松修改检测间隔、目标地址等
5. **类型安全**：完整的 TypeScript 支持
6. **可扩展**：易于添加更多网络相关功能

### 注意事项

1. **ping 权限**：确保应用有网络访问权限
2. **防火墙**：某些防火墙可能阻止 ping 请求
3. **性能影响**：网络检测会消耗一定的系统资源
4. **错误处理**：需要妥善处理网络检测失败的情况

### 后续优化建议

1. **智能检测间隔**：根据网络状态动态调整检测频率
2. **多服务器检测**：检测多个服务器提高准确性
3. **网络质量评估**：不仅检测连通性，还评估网络质量
4. **离线缓存**：实现关键数据的离线缓存功能
5. **用户设置**：允许用户自定义网络检测设置
