# 网络状态管理解决方案（简化版）

## 问题描述

当前应用为了便于更新，prerender 直接使用线上地址而不是本地静态资源。但这会导致一个问题：如果断网运行程序时会导致无法访问线上地址，出现白屏，无法重试等其他操作。

## 解决方案

### 核心思路（简化版）

1. **始终加载线上地址**：主进程启动时直接加载线上地址
2. **渲染进程处理网络状态**：使用 `@vueuse/core` 的 `useOnline` 检测网络状态
3. **组件级别切换**：Home.vue 根据网络状态自动切换显示内容
4. **用户友好的离线界面**：提供离线提示和重试功能

## 为什么选择简化方案？

### 🎯 **逻辑更清晰**

- **单一职责**：渲染进程负责网络状态检测和UI切换
- **减少复杂性**：无需主进程和渲染进程之间的复杂IPC通信
- **更好维护**：代码量大幅减少，逻辑更直观

### 🚀 **性能更好**

- **无需持续ping**：主进程不再需要每5秒ping服务器
- **响应更快**：`useOnline` 基于浏览器事件，响应更及时
- **资源消耗少**：减少了网络检测的系统资源消耗

### 🔧 **实现更简单**

- **现有基础**：Home.vue 已经实现了基于 `useOnline` 的切换逻辑
- **技术成熟**：`@vueuse/core` 是成熟的Vue生态库
- **兼容性好**：浏览器原生网络状态检测，兼容性更好

## 技术实现

### 1. 主进程简化 (`src/main/index.ts`)

```typescript
function createWindow(): void {
  // ... 窗口创建代码 ...

  // 始终加载在线地址，网络状态由渲染进程处理
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    console.log('加载在线地址，网络状态由渲染进程自动处理')
    mainWindow.loadURL(ONLINE_URL)
  }
}
```

**关键变化：**

- ❌ 移除了复杂的网络检测逻辑
- ❌ 移除了网络状态监听
- ✅ 始终加载线上地址
- ✅ 让渲染进程处理网络状态

### 2. Home.vue 组件级网络状态处理

```vue
<template>
  <MenuList v-if="online" />
  <Offline v-else />
</template>

<script setup>
import { useOnline } from '@vueuse/core'

const online = useOnline()
</script>
```

**核心逻辑：**

- ✅ 使用 `useOnline` 检测网络状态
- ✅ 在线时显示 `MenuList`
- ✅ 离线时显示 `Offline` 组件
- ✅ 自动响应网络状态变化

### 3. 简化的网络状态 Composable

```typescript
// src/renderer/src/composables/useNetworkStatus.ts
export function useNetworkStatus() {
  const online = useOnline()

  // 检查网络延迟（保留用于诊断）
  const checkLatency = async () => {
    try {
      const result = await window.api.checkLatency()
      return result
    } catch (error) {
      return { time: 0, alive: false, error: String(error) }
    }
  }

  return {
    online,
    checkLatency,
  }
}
```

**简化内容：**

- ❌ 移除了主进程网络状态管理
- ❌ 移除了IPC通信逻辑
- ✅ 保留了延迟检测（用于诊断）
- ✅ 直接使用 `useOnline`

### 4. 离线页面组件 (`src/renderer/src/components/offline.vue`)

**功能特性：**

- ✅ 显示友好的离线提示界面
- ✅ 实时显示网络状态（基于 `useOnline`）
- ✅ 提供网络延迟检测功能
- ✅ 提供页面刷新功能
- ✅ 自动重试倒计时（30秒）

## 功能特性

### ✅ 简化版功能

1. **始终加载线上地址**：主进程启动时直接加载线上地址
2. **组件级网络状态切换**：Home.vue 自动切换 MenuList 和 Offline 组件
3. **实时网络状态检测**：基于 `useOnline` 的浏览器原生检测
4. **友好的离线界面**：提供美观的离线提示页面
5. **网络延迟检测**：保留延迟检测功能用于诊断
6. **页面刷新功能**：简单有效的重试方式
7. **自动重试机制**：30秒自动刷新页面
8. **类型安全**：完整的 TypeScript 类型定义

### 🔧 配置项

```typescript
// 在线地址配置
const ONLINE_URL = 'http://**************:9027/#/consumer/home'
```

## 使用方法

### 1. 在组件中使用网络状态

```vue
<script setup>
import { useOnline } from '@vueuse/core'

const online = useOnline()

// 直接使用网络状态
console.log('当前网络状态:', online.value)
</script>
```

### 2. 使用网络状态 Composable

```vue
<script setup>
import { useNetworkStatus } from '@/composables/useNetworkStatus'

const { online, checkLatency } = useNetworkStatus()

// 检查网络延迟
const handleCheckLatency = async () => {
  const result = await checkLatency()
  console.log('延迟检测结果:', result)
}
</script>
```

## 测试方法

1. **断网测试**：

   - 断开网络连接
   - 启动应用，会加载线上地址但显示 Offline 组件
   - 恢复网络，Home.vue 自动切换回 MenuList 组件

2. **运行时断网**：

   - 应用运行时断开网络
   - Home.vue 自动切换到 Offline 组件
   - 恢复网络后自动切换回 MenuList 组件

3. **手动操作**：
   - 在离线页面点击"检测延迟"按钮测试服务器连通性
   - 点击"刷新页面"按钮重新加载应用

## 优势总结

### 🎯 **简化版优势**

1. **逻辑清晰**：单一职责，渲染进程处理网络状态
2. **性能更好**：无需主进程持续ping检测
3. **响应更快**：基于浏览器事件的网络状态检测
4. **维护简单**：代码量大幅减少，逻辑更直观
5. **用户体验好**：避免白屏，提供友好的离线提示
6. **技术成熟**：基于成熟的 `@vueuse/core` 库
7. **类型安全**：完整的 TypeScript 支持

### 🔧 **技术优势**

- ✅ **无需IPC通信**：减少了主进程和渲染进程的复杂交互
- ✅ **浏览器原生支持**：`useOnline` 基于浏览器原生API
- ✅ **自动响应**：网络状态变化时自动更新UI
- ✅ **资源节省**：不消耗额外的网络检测资源
- ✅ **兼容性好**：所有现代浏览器都支持

## 总结

简化版方案通过将网络状态检测的职责完全交给渲染进程，大大简化了架构复杂度，同时保持了良好的用户体验。这种设计更符合前端组件化的思想，每个组件负责自己的状态管理，逻辑更加清晰和可维护。
