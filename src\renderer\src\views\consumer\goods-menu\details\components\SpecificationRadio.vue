<template>
  <div class="specification-radio">
    <div
      v-for="option in options"
      :key="option.value"
      class="specification-radio-item transition-all duration-200 active:scale-[0.95]"
      :class="{
        'specification-radio-active': selectedValue === option.value,
        'specification-radio-disabled': option.disabled,
      }"
      @click="handleSelect(option)"
    >
      {{ option.label }}
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  options: { type: Array, default: () => [] },
  defaultValue: { type: String },
})

const emit = defineEmits(['change'])

const selectedValue = ref(props.defaultValue)

function handleSelect(option) {
  if (option.disabled) return
  selectedValue.value = option.value
}

watch(selectedValue, (newValue) => {
  emit('change', newValue)
})
</script>

<style scoped>
.specification-radio {
  display: flex;
  gap: 10px;
}

.specification-radio-item {
  width: 120px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f5f6f8;
  border-radius: 6px;
  cursor: pointer;
  font-size: 20px;
  user-select: none;
  transition: all 0.2s ease;
}

.specification-radio-active {
  border: 1px solid #6d3202;
  background-color: rgba(109, 50, 2, 0.1);
  color: #6d3202;
}

.specification-radio-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.specification-radio-item:hover:not(.specification-radio-disabled) {
  border-color: #d1d5db;
}

.specification-radio-active:hover {
  border-color: #6d3202;
}
</style>
