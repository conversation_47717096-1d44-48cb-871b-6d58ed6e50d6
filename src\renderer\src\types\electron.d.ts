export interface IElectronAPI {
  createWindow: () => Promise<void>
  toggleKiosk: (windowId?: number) => Promise<void>
}

export interface IApi {
  // 虚拟键盘
  openVirtualKeyboard: () => void
  closeVirtualKeyboard: () => void

  // 窗口管理
  getWindowId: () => Promise<number>
  enterKiosk: () => Promise<boolean>
  exitKiosk: () => Promise<boolean>

  // 自启动
  setAutoLaunch: (enable: boolean) => Promise<void>
  getAutoLaunch: () => Promise<boolean>

  // 网络延迟检测（保留用于诊断）
  checkLatency: () => Promise<{ time: number; alive: boolean } | { error: string }>
}

declare global {
  interface Window {
    electronAPI: IElectronAPI
    api: IApi
  }
}
