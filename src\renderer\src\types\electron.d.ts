export interface IElectronAPI {
  createWindow: () => Promise<void>
  toggleKiosk: (windowId?: number) => Promise<void>
}

export interface IApi {
  // 虚拟键盘
  openVirtualKeyboard: () => void
  closeVirtualKeyboard: () => void

  // 窗口管理
  getWindowId: () => Promise<number>
  enterKiosk: () => Promise<boolean>
  exitKiosk: () => Promise<boolean>

  // 自启动
  setAutoLaunch: (enable: boolean) => Promise<void>
  getAutoLaunch: () => Promise<boolean>

  // 网络检测
  checkLatency: (url: string) => Promise<{ time: number; alive: boolean } | { error: string }>
  getNetworkStatus: () => Promise<boolean>
  checkNetworkStatus: () => Promise<boolean>
  reloadOnlineUrl: () => Promise<boolean>

  // 网络状态监听
  onNetworkStatusChanged: (callback: (isOnline: boolean) => void) => void
  removeNetworkStatusListener: () => void
}

declare global {
  interface Window {
    electronAPI: IElectronAPI
    api: IApi
  }
}
