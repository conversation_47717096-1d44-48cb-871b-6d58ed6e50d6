<template>
  <div class="flex w-full flex-col select-none">
    <GoodsImage
      :src="goods.imagePath"
      width="var(--goods-item-width, 100%)"
      height="var(--goods-item-height, 100%)"
    >
      <div
        v-if="promotion?.labelContent"
        class="absolute top-2 left-2 bg-white flex items-center justify-center px-1 rounded border border-[#6D320233]"
      >
        <span class="text-[#6D3202] text-sm">
          {{ promotion?.labelContent }}
        </span>
      </div>
    </GoodsImage>
    <div class="flex-1 p-4">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <span class="block text-[20px] font-bold text-gray-900">
            {{ goods.productName }}
          </span>
        </div>
      </div>
      <div class="mt-2 flex justify-between items-center text-[#6D3202]">
        <div>
          <span class="text-2xl font-semibold">
            ¥{{ promotion?.promotionalPrice || goods.price }}
          </span>
          <span
            v-if="promotion && promotion?.promotionalPrice !== goods.price"
            class="text-sm line-through text-gray-600 ml-1"
          >
            ¥{{ goods.price }}
          </span>
        </div>
        <template v-if="referrer === 'menu'">
          <div
            v-if="(goods as GoodsItem).status === GoodsStatus.ON_SALE"
            class="w-[28px] h-[28px] bg-[#6D3202] rounded-full flex items-center justify-center text-white text-2xl cursor-pointer"
          >
            +
          </div>
          <button
            v-else
            class="w-[70px] h-[28px] px-2 rounded-full bg-[#bdbdbf] text-sm text-white flex items-center justify-center m-0"
            disabled
          >
            {{ (goods as GoodsItem).status === GoodsStatus.MAINTENANCE ? '维护中' : '已售罄' }}
          </button>
        </template>
        <template v-if="referrer === 'cart'">
          <div class="flex items-center justify-space">
            <div
              class="w-6 h-6 flex items-center justify-center rounded-full"
              @click.stop="decreaseFromCart(goods as CartItem, beforeDecrease)"
            >
              <wd-icon name="minus-circle" size="24px" custom-class="text-[#6D3202]" />
            </div>
            <div class="w-8 text-center mr-1">{{ (goods as CartItem).$quantity }}</div>
            <div
              class="w-6 h-6 flex items-center justify-center bg-[#6D3202] rounded-full"
              @click.stop="addToCart(goods as CartItem)"
            >
              <wd-icon name="add" size="14px" custom-class="text-white" />
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// 商品 item 菜单 购物车 订单详情使用
import { GoodsStatus, GoodsItem, GoodsDetails, CartItem } from '../../types'
import GoodsImage from '@/views/consumer/components/GoodsImage.vue'
import { useCart } from '../../useCart'
import { computed } from 'vue'
import Big from 'big.js'

const beforeDecrease = () => {
  return Promise.resolve(true)
}

const props = defineProps<
  { goods: GoodsItem; referrer: 'menu' } | { goods: GoodsDetails; referrer: 'cart' | 'order' }
>()

const { addToCart, decreaseFromCart } = useCart()

// 计算商品促销价
// 计算商品促销价
const promotion = computed(() => {
  const promotionalList = props.goods?.promotionalList
    ?.filter(
      (item) =>
        // 特价促销
        (item.promotionType === '1' && item.goodsId === props.goods.id) ||
        // 打折促销
        item.promotionType === '0' ||
        // 第N杯折扣, 第一杯折扣
        (item.promotionType === '2' && +item.businessParam === 1),
    )
    .map((item) => ({ ...item }))
  const price = props.goods.price
  const promotionalPriceList = [...promotionalList].map((item) => {
    switch (item.promotionType) {
      case '0':
        return Big(price).times(item.businessValue).times(0.1).toNumber()
      case '1':
        return item.promotionalPrice
      case '2':
        return Big(price).times(item.businessValue).times(0.1).toNumber()
      default:
        return price
    }
  })

  if (promotionalPriceList.length > 0) {
    const minPriceIndex = promotionalPriceList.indexOf(Math.min(...promotionalPriceList))
    const promotion = promotionalList[minPriceIndex]
    return {
      ...promotion,
      promotionalPrice:
        promotionalPriceList[minPriceIndex] < 0.01 ? 0.01 : promotionalPriceList[minPriceIndex],
    }
  }
  return null
})
</script>
