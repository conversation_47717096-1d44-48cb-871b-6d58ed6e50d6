<template>
  <Popup v-model:visible="show">
    <template v-if="status === 'pending'">
      <div class="relative w-full h-full flex items-center justify-center flex-col">
        <div
          class="flex flex-col w-[340px] h-[472px] box-border items-center justify-center bg-white rounded-xl"
        >
          <!-- 正在扫码支付 -->

          <QRCode
            v-if="qrCode"
            :value="qrCode"
            :size="250"
            :color="'#000000'"
            :bg-color="'#ffffff'"
            :margin="4"
          />

          <div class="text-lg font-bold text-[#6D3202] ml-1 my-5">
            <span class="text-[24px]">¥</span>
            <span class="text-[36px]">{{ total }}</span>
          </div>
          <div
            class="w-[80px] h-[32px] rounded-full text-[20px] text-gray-900 flex items-center justify-center border border-gray-900 border-solid"
          >
            {{ time }}S
          </div>
          <div class="mt-2.5 text-[24px] text-gray-900 font-bold">微信扫码支付</div>
        </div>

        <button
          class="w-[140px] h-[40px] border-solid border-white border-1 text-white text-[20px] rounded-full mt-10 active:scale-95 transition-all duration-300"
          @click="handleCancelPayment"
        >
          取消支付
        </button>
      </div>
    </template>

    <!-- 支付成功 -->
    <template v-else>
      <div
        class="flex flex-col items-center justify-center gap-10 w-[340px] h-[472px] box-border bg-white rounded-xl"
      >
        <img :src="SuccessIcon" class="w-[120px] h-[120px]" />
        <div class="text-[32px] font-bold">支付成功</div>
      </div>
    </template>
  </Popup>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SuccessIcon from './imgs/success.png'
import Popup from '@/components/Popup.vue'
import QRCode from '@/components/QRCode.vue'
import { useCart } from '@/views/consumer/goods-menu/useCart'
import { useCountdown } from '@/hooks/useCountdown'
import { useRouter } from 'vue-router'
import { useIntervalFn, useTimeoutFn } from '@vueuse/core'
import { queryPaymentResult, deleteCacheOrder } from '@/api/consumer'
import { OrderStatus } from '@/types/consumer'
const emit = defineEmits(['close'])
const router = useRouter()
const { total, clearCart } = useCart()
// 支付弹窗时间倒计时
const { time, pause, resume, reset } = useCountdown(
  120,
  async () => {
    await handleCancelPayment()
  },
  false,
)

pause()

const show = ref(false)
// 当前状态
const status = ref('pending')
// sign
const sign = ref('')
// 二维码
const qrCode = ref('')
// 购物车
// 请求订单状态
const requestOrderStatus = async () => {
  const { data } = await queryPaymentResult(sign.value)
  const productOrderInfo = data.value.data?.productOrderInfo
  if (
    productOrderInfo &&
    [OrderStatus.PAID, OrderStatus.COMPLETED].includes(
      productOrderInfo?.orderStatus?.toString() as OrderStatus,
    )
  ) {
    status.value = 'success'
    useTimeoutFn(() => {
      clearCart()
      router.push({
        query: { sign: sign.value, orderId: productOrderInfo?.id },
        path: '/consumer/processing',
      })
    }, 3000)
  }
}

// 定时轮询支付状态
const { pause: pauseRequestOrderStatus, resume: resumeRequestOrderStatus } = useIntervalFn(
  requestOrderStatus,
  1000 * 5,
)

pauseRequestOrderStatus()

defineExpose({
  show: (orderId: string) => {
    resume()
    reset()
    show.value = true
    sign.value = orderId
    qrCode.value = `${import.meta.env.VITE_MINI_PROGRAM_URL}/payments?sign=${orderId}`
    resumeRequestOrderStatus()
  },
  hide: () => {
    pauseRequestOrderStatus()
    show.value = false
    emit('close')
  },
})
// 取消支付
const handleCancelPayment = async () => {
  pauseRequestOrderStatus()
  await deleteCacheOrder(sign.value)
  show.value = false
  emit('close')
}
</script>
