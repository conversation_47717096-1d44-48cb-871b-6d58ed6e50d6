<script setup lang="ts">
import 'swiper/css'
import 'swiper/css/free-mode'
import 'swiper/css/scrollbar'
import 'swiper/css/pagination'
import 'swiper/css/navigation'
import { onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useNetworkStatus } from './composables/useNetworkStatus'

const router = useRouter()
const route = useRoute()
const { getOverallNetworkStatus } = useNetworkStatus()

// 监听网络状态变化，自动切换页面
watch(
  () => getOverallNetworkStatus(),
  (isOnline) => {
    console.log('App: 网络状态变化', isOnline)

    // 如果网络断开且不在离线页面，跳转到离线页面
    if (!isOnline && route.name !== 'Offline') {
      console.log('网络断开，跳转到离线页面')
      router.push('/offline')
    }
    // 如果网络恢复且在离线页面，跳转到首页
    else if (isOnline && route.name === 'Offline') {
      console.log('网络恢复，跳转到首页')
      router.push('/consumer/home')
    }
  },
  { immediate: true },
)

onMounted(() => {
  console.log('App mounted, 初始网络状态:', getOverallNetworkStatus())
})
</script>

<template>
  <router-view />
</template>

<style>
#app {
  height: 100vh;
}
/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-thumb {
  background: rgba(144, 147, 153, 0.3);
  border-radius: 4px;
  transition: background 0.3s;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(144, 147, 153, 0.5);
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}
.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  height: auto;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
</style>
