<template>
  <div class="w-full h-full flex flex-col justify-center items-center overflow-hidden bg-gray-50">
    <div class="flex flex-col items-center space-y-6 p-8">
      <!-- 离线图标 -->
      <div class="w-24 h-24 rounded-full bg-red-100 flex items-center justify-center">
        <svg class="w-12 h-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M18.364 5.636l-12.728 12.728m0-12.728l12.728 12.728M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2z"
          />
        </svg>
      </div>

      <!-- 状态文本 -->
      <div class="text-center">
        <div class="text-[#6d3202] text-[28px] font-bold mb-2">设备离线</div>
        <div class="text-[#494949] text-[16px] font-medium">请检查设备是否连接网络</div>
      </div>

      <!-- 网络状态信息 -->
      <div class="text-center space-y-2">
        <div class="text-sm text-gray-600">
          主进程网络状态:
          <span :class="isMainProcessOnline ? 'text-green-600' : 'text-red-600'">
            {{ isMainProcessOnline ? '在线' : '离线' }}
          </span>
        </div>
        <div class="text-sm text-gray-600">
          浏览器网络状态:
          <span :class="browserOnline ? 'text-green-600' : 'text-red-600'">
            {{ browserOnline ? '在线' : '离线' }}
          </span>
        </div>
      </div>

      <!-- 重试按钮 -->
      <div class="flex space-x-4">
        <button
          @click="handleRetry"
          :disabled="isChecking"
          class="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          {{ isChecking ? '检查中...' : '重试连接' }}
        </button>

        <button
          @click="handleReload"
          :disabled="!isMainProcessOnline"
          class="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          重新加载
        </button>
      </div>

      <!-- 自动重试提示 -->
      <div v-if="autoRetryCountdown > 0" class="text-sm text-gray-500">
        {{ autoRetryCountdown }} 秒后自动重试
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useNetworkStatus } from '../composables/useNetworkStatus'

const { isMainProcessOnline, browserOnline, checkNetworkStatus, reloadOnlineUrl } =
  useNetworkStatus()

const isChecking = ref(false)
const autoRetryCountdown = ref(0)
let autoRetryTimer: NodeJS.Timeout | null = null
let countdownTimer: NodeJS.Timeout | null = null

// 手动重试连接
const handleRetry = async () => {
  if (isChecking.value) return

  isChecking.value = true
  try {
    const status = await checkNetworkStatus()
    console.log('手动检查网络状态:', status)

    if (status) {
      // 网络恢复，尝试重新加载
      await handleReload()
    }
  } catch (error) {
    console.error('重试连接失败:', error)
  } finally {
    isChecking.value = false
  }
}

// 重新加载在线地址
const handleReload = async () => {
  try {
    const result = await reloadOnlineUrl()
    if (result) {
      console.log('重新加载在线地址成功')
    } else {
      console.log('重新加载在线地址失败')
    }
  } catch (error) {
    console.error('重新加载失败:', error)
  }
}

// 启动自动重试倒计时
const startAutoRetry = () => {
  if (autoRetryTimer) {
    clearTimeout(autoRetryTimer)
  }
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }

  autoRetryCountdown.value = 30 // 30秒后自动重试

  countdownTimer = setInterval(() => {
    autoRetryCountdown.value--
    if (autoRetryCountdown.value <= 0) {
      clearInterval(countdownTimer!)
      countdownTimer = null
    }
  }, 1000)

  autoRetryTimer = setTimeout(() => {
    handleRetry()
    startAutoRetry() // 继续下一轮自动重试
  }, 30000)
}

// 清理定时器
const cleanup = () => {
  if (autoRetryTimer) {
    clearTimeout(autoRetryTimer)
    autoRetryTimer = null
  }
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

onMounted(() => {
  // 启动自动重试
  startAutoRetry()
})

onUnmounted(() => {
  cleanup()
})
</script>
