<template>
  <div class="flex-1 overflow-hidden flex flex-col h-full bg-white p-5 gap-5">
    <!-- 顶部分类导航 -->
    <CategoryTabs v-model:active-index="activeCategory" :categories="categories" />
    <Swiper class="flex-1" @swiper="setOuterSwiperInstance" @slide-change="handleOuterSlideChange">
      <SwiperSlide v-for="category in categories" :key="category.typeName">
        <Swiper v-bind="swiperOptions" :modules="[FreeMode, Scrollbar, Mousewheel]">
          <SwiperSlide>
            <div class="grid grid-cols-5 gap-5">
              <div
                v-for="goods in category.goodsMenuVOList"
                :key="goods.id"
                class="transition-transform duration-200 active:scale-[0.98] bg-white rounded-lg overflow-hidden shadow-sm"
                @click="handleGoodsClick(goods)"
              >
                <GoodsItem :goods="goods" referrer="menu" />
              </div>
            </div>
          </SwiperSlide>
        </Swiper>
      </SwiperSlide>
    </Swiper>

    <!-- 商品内容区 -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import GoodsItem from '../goods-item/index.vue'
import CategoryTabs from './CategoryTabs.vue'
import type { GoodsItem as GoodsItemType } from '../../types'
import { GoodsStatus } from '@/views/consumer/goods-menu/types'
import { fetchMenuListByCarId } from '@/api/consumer'
import { toast } from '@/libs/toast'
import { Swiper, SwiperSlide } from 'swiper/vue'
import type { Swiper as SwiperInstance } from 'swiper'
import type { SwiperOptions } from 'swiper/types'
import { FreeMode, Scrollbar, Mousewheel } from 'swiper/modules'
import { useIntervalFn } from '@vueuse/core'

const props = defineProps<{ carId: string }>()

interface Category {
  // 排序
  sort: number
  // 菜单名称
  typeName: string
  // 菜单
  type: string
  // 商品列表
  goodsMenuVOList: GoodsItemType[]
}

const swiperOptions: SwiperOptions = {
  direction: 'vertical',
  slidesPerView: 'auto',
  freeMode: true,
  scrollbar: true,
  mousewheel: true,
}

// 定义事件
const emit = defineEmits<{
  (e: 'select', goods: GoodsItemType): void
}>()

// 商品数据
const categories = ref<Category[]>([])
// 当前选中的分类
const activeCategory = ref(0)
// 外层轮播实例
const outerSwiperInstance = ref<SwiperInstance | null>(null)

// 请求菜单列表
const fetchMenuData = async () => {
  try {
    const res = await fetchMenuListByCarId({ carId: props.carId })
    const remoteData = res.data.value.data
    // 对商品列表根据id去重
    const uniqueGoodsMenuVOList = remoteData
      .map((item) => item.goodsMenuVOList)
      .flat()
      .reduce((unique: GoodsItemType[], item) => {
        return unique.some((u) => u.id === item.id) ? unique : [...unique, item]
      }, [])

    // 添加全部tab 合并所有数据
    categories.value = [
      {
        sort: -1,
        type: 'All',
        typeName: '全部',
        goodsMenuVOList: uniqueGoodsMenuVOList,
      },
      ...remoteData,
    ]
  } catch (error) {
    toast('获取菜单失败，请重试')
    console.error('Failed to fetch menu:', error)
  }
}

// 每30秒更新一次菜单数据
useIntervalFn(fetchMenuData, 1000 * 30)

onMounted(() => {
  fetchMenuData()
})

// 处理商品点击
const handleGoodsClick = (goods: GoodsItemType) => {
  if (goods.status !== GoodsStatus.ON_SALE) {
    toast(goods.status === GoodsStatus.MAINTENANCE ? '商品维护中' : '商品已售罄')
    // return
  }
  emit('select', goods)
}

// 设置外层Swiper实例
const setOuterSwiperInstance = (swiper: SwiperInstance) => {
  outerSwiperInstance.value = swiper
}

// 处理外层Swiper滑动改变事件
const handleOuterSlideChange = () => {
  if (outerSwiperInstance.value) {
    activeCategory.value = outerSwiperInstance.value.activeIndex
  }
}

// 监听分类变化,控制外层Swiper切换
watch(activeCategory, (newIndex) => {
  if (outerSwiperInstance.value && outerSwiperInstance.value.activeIndex !== newIndex) {
    outerSwiperInstance.value.slideTo(newIndex)
  }
})
</script>

<script lang="ts">
export default { options: { styleIsolation: 'shared' } }
</script>
