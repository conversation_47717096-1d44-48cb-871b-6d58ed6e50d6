import { useCartStore } from '@/stores/cart'
import type { CartItem, GoodsDetails } from '@/views/consumer/goods-menu/types'
import { computed } from 'vue'
import { calculateLowestPrice } from '@/api/consumer'

export function useCart() {
  const cartStore = useCartStore()

  // increase 是否是数量+1操作
  const addToCart = (item: CartItem, increase: boolean = true) => {
    if (increase) {
      cartStore.addItem({ ...item, $quantity: 1 })
    } else {
      cartStore.addItem(item)
    }
    calculatePrice()
  }

  const decreaseFromCart = async (item: CartItem, onLastItem?: () => Promise<boolean>) => {
    cartStore.decreaseItem(item, onLastItem)
    calculatePrice()
  }

  const removeFromCart = (item: CartItem) => {
    cartStore.removeItem(item)
  }

  const clearCart = () => {
    cartStore.clearCart()
  }

  const total = computed(() => {
    return cartStore.total
  })

  const totalCount = computed(() => {
    return cartStore.totalCount
  })

  const setGoodsDetails = (goodsId: string, fetchDetail: Promise<GoodsDetails>) => {
    cartStore.setGoodsDetails(goodsId, fetchDetail)
  }

  const getGoodsDetails = (id: string | number) => {
    return cartStore.getGoodsDetails(id)
  }

  const computedPrice = computed(() => cartStore.computedPrice)

  const calculatePrice = async () => {
    const carInfo = JSON.parse(localStorage.getItem('carInfo') || '{}')
    const res: any = await calculateLowestPrice({
      areaId: carInfo.runAreaId,
      carId: carInfo.id,
      goodsInfo: cartStore.items.map((item) => {
        // @ts-ignore
        const { id: skuId } = item.cupPriceList.find(
          (i) =>
            i.cupTypeCode === item.$specification.cup && i.temperature === item.$specification.temp,
        )
        return {
          goodsId: item.id,
          number: item.$quantity,
          skuId,
        }
      }),
    })
    cartStore.setComputedPrice(res.data?.value?.data as never)
    return res.data?.value?.data
  }

  return {
    items: computed(() => cartStore.items),
    computedPrice,
    total,
    totalCount,
    addToCart,
    backupCart: cartStore.backupCart,
    restoreCart: cartStore.restoreCart,
    decreaseFromCart,
    removeFromCart,
    clearCart,
    setGoodsDetails,
    getGoodsDetails,
  }
}
