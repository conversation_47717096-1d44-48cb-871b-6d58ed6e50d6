<template>
  <div class="flex flex-col gap-4 bg-[#D3E8FE] w-[340px] h-[500px] rounded-xl overflow-hidden">
    <Swiper
      ref="swiperRef"
      :slides-per-view="1"
      :pagination="{ dynamicBullets: true }"
      :autoplay="autoplayConfig"
      :modules="modules"
      @slide-change="handleSlideChange"
      @swiper="onSwiperInit"
    >
      <SwiperSlide v-for="(item, index) in swiperList" :key="item.filePath + index">
        <img
          v-if="item.type === 'picture'"
          :src="item.filePath || DefaultAd"
          alt="广告"
          class="w-[340px] h-[520px] object-cover rounded-2xl"
        />
        <video
          v-else
          :ref="(el) => setVideoRef(el, index)"
          :src="item.filePath"
          muted
          playsinline
          class="w-[340px] h-[520px] rounded-2xl"
          @ended="handleVideoEnded"
        ></video>
      </SwiperSlide>
    </Swiper>
  </div>
</template>

<script setup lang="ts">
import { fetchQueryAdvertises } from '@/api/consumer'
import DefaultAd from './imgs/ad.png'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Pagination, Autoplay } from 'swiper/modules'
import { ref, onMounted } from 'vue'

// 类型声明
interface AdMaterial {
  id?: string
  jumpPath?: string
  filePath: string
  type: 'video' | 'picture'
}

interface AdMaterialRecord {
  dispatchAdMaterialVOList: AdMaterial[]
  taskId: string
  timeInterval?: string
}

interface AdMaterialList extends AdMaterial {
  taskId?: string
  interval?: number
}

const modules = [Pagination, Autoplay]
const swiperList = ref<AdMaterialList[]>([{ filePath: DefaultAd, type: 'picture' }])
const carId = ref(localStorage.getItem('carId'))
const swiperRef = ref()
const currentSlideIndex = ref(0)
const videoRefs = ref<HTMLVideoElement[]>([])

// 动态 autoplay 配置
const autoplayConfig = ref({
  delay: 3000,
  disableOnInteraction: false,
  waitForTransition: false,
})

// 设置视频引用
const setVideoRef = (el: HTMLVideoElement | null, index: number) => {
  if (el) videoRefs.value[index] = el
}

// 初始化 Swiper
const onSwiperInit = (swiper) => {
  swiperRef.value = swiper
}

// 切换幻灯片时处理视频播放
const handleSlideChange = async (swiper) => {
  currentSlideIndex.value = swiper.activeIndex
  const currentItem = swiperList.value[currentSlideIndex.value]
  if (currentItem.type === 'picture') {
    autoplayConfig.value.delay = currentItem.interval ?? 30000
    swiper.autoplay.start()
  } else {
    swiper.autoplay.stop()
    // 强制播放当前视频
    await playCurrentVideo()
  }
}

// 播放当前视频
const playCurrentVideo = async () => {
  const video = videoRefs.value[currentSlideIndex.value]
  if (!video) return

  try {
    await video.play()
  } catch (err) {
    console.error('视频播放失败:', err)
    // 如果播放失败，3秒后重试（兼容某些浏览器策略）
    setTimeout(() => video.play(), 20000)
  }
}

// 视频结束处理（修复最后一项不循环的问题）
const handleVideoEnded = () => {
  if (!swiperRef.value) return

  const isLastSlide = currentSlideIndex.value === swiperList.value.length - 1
  if (isLastSlide) {
    // 如果是最后一项，手动切换到第一张
    swiperRef.value.slideTo(0)
  } else {
    // 否则正常切换到下一张
    swiperRef.value.slideNext()
  }
}

// 获取广告数据
const fetchAd = () => {
  fetchQueryAdvertises({
    scopId: carId.value as string,
    scopType: 'scop_car',
    taskMaterialType: 'touchScreenHome',
  }).then(({ data }) => {
    if (!data.value?.success) return
    const records = data.value.data
    const adList: AdMaterialList[] = []
    records.forEach((record: AdMaterialRecord) => {
      record.dispatchAdMaterialVOList.forEach((item: AdMaterial) => {
        adList.push({
          filePath: item.filePath,
          jumpPath: item.jumpPath,
          type: item.type,
          id: item.id,
          taskId: record.taskId,
          interval: Number(`${record.timeInterval}000`),
        })
      })
    })
    swiperList.value = adList.length ? adList : [{ filePath: DefaultAd, type: 'picture' }]
  })
}

onMounted(fetchAd)
</script>
