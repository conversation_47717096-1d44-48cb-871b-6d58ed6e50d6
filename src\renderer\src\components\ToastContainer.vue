<template>
  <Teleport to="#app">
    <Transition
      enter-active-class="transition duration-300 ease-out"
      enter-from-class="transform scale-95 opacity-0"
      enter-to-class="transform scale-100 opacity-100"
      leave-active-class="transition duration-200 ease-in"
      leave-from-class="transform scale-100 opacity-100"
      leave-to-class="transform scale-95 opacity-0"
    >
      <div
        v-if="visible"
        class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-800 text-white px-4 py-2 rounded-md shadow-lg z-9990 min-w-[200px] text-center"
      >
        {{ message }}
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useTimeoutFn } from '@vueuse/core'

const props = defineProps<{
  message: string
  duration?: number
  visible: boolean
}>()

const emit = defineEmits<{
  close: []
}>()

// 监听visible属性的变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 当Toast显示时，设置定时器在指定时间后自动关闭
      const { start } = useTimeoutFn(() => {
        emit('close')
      }, props.duration || 3000)

      start()
    }
  },
)
</script>
