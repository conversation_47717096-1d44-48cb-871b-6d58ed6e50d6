<template>
  <div class="p-6 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">网络状态管理演示</h1>
    
    <!-- 网络状态显示 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div class="bg-white p-4 rounded-lg shadow">
        <h3 class="font-semibold mb-2">综合网络状态</h3>
        <div :class="getOverallNetworkStatus() ? 'text-green-600' : 'text-red-600'" class="text-lg font-bold">
          {{ getOverallNetworkStatus() ? '在线' : '离线' }}
        </div>
      </div>
      
      <div class="bg-white p-4 rounded-lg shadow">
        <h3 class="font-semibold mb-2">主进程网络状态</h3>
        <div :class="isMainProcessOnline ? 'text-green-600' : 'text-red-600'" class="text-lg font-bold">
          {{ isMainProcessOnline ? '在线' : '离线' }}
        </div>
      </div>
      
      <div class="bg-white p-4 rounded-lg shadow">
        <h3 class="font-semibold mb-2">浏览器网络状态</h3>
        <div :class="browserOnline ? 'text-green-600' : 'text-red-600'" class="text-lg font-bold">
          {{ browserOnline ? '在线' : '离线' }}
        </div>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="bg-white p-6 rounded-lg shadow mb-6">
      <h3 class="font-semibold mb-4">网络操作</h3>
      <div class="flex flex-wrap gap-4">
        <button 
          @click="handleCheckStatus"
          :disabled="isChecking"
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400"
        >
          {{ isChecking ? '检查中...' : '检查网络状态' }}
        </button>
        
        <button 
          @click="handleReload"
          :disabled="!isMainProcessOnline"
          class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-400"
        >
          重新加载在线地址
        </button>
        
        <button 
          @click="handleCheckLatency"
          :disabled="isCheckingLatency"
          class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:bg-gray-400"
        >
          {{ isCheckingLatency ? '检测中...' : '检测网络延迟' }}
        </button>
      </div>
    </div>
    
    <!-- 网络延迟信息 -->
    <div v-if="latencyInfo" class="bg-white p-6 rounded-lg shadow mb-6">
      <h3 class="font-semibold mb-4">网络延迟信息</h3>
      <div v-if="latencyInfo.error" class="text-red-600">
        错误: {{ latencyInfo.error }}
      </div>
      <div v-else class="space-y-2">
        <div>延迟: <span class="font-mono">{{ latencyInfo.time }}ms</span></div>
        <div>状态: <span :class="latencyInfo.alive ? 'text-green-600' : 'text-red-600'">
          {{ latencyInfo.alive ? '可达' : '不可达' }}
        </span></div>
      </div>
    </div>
    
    <!-- 网络状态变化日志 -->
    <div class="bg-white p-6 rounded-lg shadow">
      <h3 class="font-semibold mb-4">网络状态变化日志</h3>
      <div class="max-h-64 overflow-y-auto">
        <div v-if="statusLogs.length === 0" class="text-gray-500">
          暂无日志
        </div>
        <div v-for="(log, index) in statusLogs" :key="index" class="mb-2 p-2 bg-gray-50 rounded text-sm">
          <span class="text-gray-600">{{ log.timestamp }}</span> - 
          <span :class="log.isOnline ? 'text-green-600' : 'text-red-600'">
            {{ log.isOnline ? '网络连接' : '网络断开' }}
          </span>
        </div>
      </div>
      <button 
        @click="clearLogs"
        class="mt-4 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
      >
        清空日志
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useNetworkStatus } from '../composables/useNetworkStatus'

const { 
  isMainProcessOnline, 
  browserOnline, 
  checkNetworkStatus, 
  reloadOnlineUrl,
  getOverallNetworkStatus
} = useNetworkStatus()

const isChecking = ref(false)
const isCheckingLatency = ref(false)
const latencyInfo = ref<{ time: number; alive: boolean; error?: string } | null>(null)
const statusLogs = ref<Array<{ timestamp: string; isOnline: boolean }>>([])

// 检查网络状态
const handleCheckStatus = async () => {
  if (isChecking.value) return
  
  isChecking.value = true
  try {
    const status = await checkNetworkStatus()
    console.log('网络状态检查结果:', status)
    addStatusLog(status)
  } catch (error) {
    console.error('检查网络状态失败:', error)
  } finally {
    isChecking.value = false
  }
}

// 重新加载
const handleReload = async () => {
  try {
    const result = await reloadOnlineUrl()
    console.log('重新加载结果:', result)
  } catch (error) {
    console.error('重新加载失败:', error)
  }
}

// 检测网络延迟
const handleCheckLatency = async () => {
  if (isCheckingLatency.value) return
  
  isCheckingLatency.value = true
  try {
    const result = await window.api.checkLatency('')
    latencyInfo.value = result
  } catch (error) {
    console.error('检测网络延迟失败:', error)
    latencyInfo.value = { time: 0, alive: false, error: String(error) }
  } finally {
    isCheckingLatency.value = false
  }
}

// 添加状态日志
const addStatusLog = (isOnline: boolean) => {
  const timestamp = new Date().toLocaleTimeString()
  statusLogs.value.unshift({ timestamp, isOnline })
  
  // 保持最多50条日志
  if (statusLogs.value.length > 50) {
    statusLogs.value = statusLogs.value.slice(0, 50)
  }
}

// 清空日志
const clearLogs = () => {
  statusLogs.value = []
}

// 监听网络状态变化
const handleNetworkStatusChange = (isOnline: boolean) => {
  addStatusLog(isOnline)
}

onMounted(() => {
  // 监听网络状态变化
  window.api.onNetworkStatusChanged(handleNetworkStatusChange)
})
</script>
