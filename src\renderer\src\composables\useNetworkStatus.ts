import { ref, onMounted, onUnmounted } from 'vue'
import { useOnline } from '@vueuse/core'

export function useNetworkStatus() {
  const isOnline = ref(false)
  const isMainProcessOnline = ref(false)
  const browserOnline = useOnline()

  // 获取主进程的网络状态
  const getMainProcessNetworkStatus = async () => {
    try {
      const status = await window.api.getNetworkStatus()
      isMainProcessOnline.value = status
      return status
    } catch (error) {
      console.error('获取主进程网络状态失败:', error)
      return false
    }
  }

  // 手动检查网络状态
  const checkNetworkStatus = async () => {
    try {
      const status = await window.api.checkNetworkStatus()
      isMainProcessOnline.value = status
      return status
    } catch (error) {
      console.error('检查网络状态失败:', error)
      return false
    }
  }

  // 重新加载在线地址
  const reloadOnlineUrl = async () => {
    try {
      const result = await window.api.reloadOnlineUrl()
      return result
    } catch (error) {
      console.error('重新加载在线地址失败:', error)
      return false
    }
  }

  // 网络状态变化处理
  const handleNetworkStatusChange = (status: boolean) => {
    console.log(`主进程网络状态变化: ${status ? '在线' : '离线'}`)
    isMainProcessOnline.value = status
    
    // 如果网络恢复且当前页面是离线页面，可以选择重新加载
    if (status && window.location.hash.includes('offline')) {
      console.log('网络恢复，准备重新加载在线地址')
      // 这里可以添加一些用户提示，比如显示"网络已恢复，正在重新连接..."
    }
  }

  // 综合判断网络状态（主进程检测 + 浏览器检测）
  const getOverallNetworkStatus = () => {
    // 优先使用主进程的网络状态，因为它检测的是实际的服务器连通性
    return isMainProcessOnline.value && browserOnline.value
  }

  onMounted(async () => {
    // 获取初始网络状态
    await getMainProcessNetworkStatus()
    
    // 监听主进程的网络状态变化
    window.api.onNetworkStatusChanged(handleNetworkStatusChange)
    
    // 设置综合网络状态
    isOnline.value = getOverallNetworkStatus()
  })

  onUnmounted(() => {
    // 清理监听器
    window.api.removeNetworkStatusListener()
  })

  return {
    isOnline: isOnline,
    isMainProcessOnline: isMainProcessOnline,
    browserOnline: browserOnline,
    getMainProcessNetworkStatus,
    checkNetworkStatus,
    reloadOnlineUrl,
    getOverallNetworkStatus
  }
}
