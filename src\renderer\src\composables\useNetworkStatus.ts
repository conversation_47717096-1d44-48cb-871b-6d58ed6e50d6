import { useOnline } from '@vueuse/core'

export function useNetworkStatus() {
  const online = useOnline()

  // 检查网络延迟（保留用于诊断）
  const checkLatency = async () => {
    try {
      const result = await window.api.checkLatency()
      return result
    } catch (error) {
      console.error('检测网络延迟失败:', error)
      return { time: 0, alive: false, error: String(error) }
    }
  }

  return {
    online,
    checkLatency,
  }
}
