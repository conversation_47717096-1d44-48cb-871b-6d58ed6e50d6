import { contextBridge, ipc<PERSON>ender<PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
// Custom APIs for renderer
const api = {
  // 添加虚拟键盘相关API
  openVirtualKeyboard: () => ipcRenderer.send('open-virtual-keyboard'),
  closeVirtualKeyboard: () => ipcRenderer.send('close-virtual-keyboard'),
  // 添加获取窗口ID的方法
  getWindowId: () => ipcRenderer.invoke('get-window-id'),
  // 添加kiosk模式控制API
  enterKiosk: () => ipcRenderer.invoke('enter-kiosk'),
  exitKiosk: () => ipcRenderer.invoke('exit-kiosk'),
  // 添加开机自启动控制API
  setAutoLaunch: (enable: boolean) => ipcRenderer.invoke('set-auto-launch', enable),
  getAutoLaunch: () => ipcRenderer.invoke('get-auto-launch'),
  // 添加检查网络延迟API
  checkLatency: (url: string) => ipcRenderer.invoke('check-latency', url),
  // 添加网络状态相关API
  getNetworkStatus: () => ipcRenderer.invoke('get-network-status'),
  checkNetworkStatus: () => ipcRenderer.invoke('check-network-status'),
  reloadOnlineUrl: () => ipcRenderer.invoke('reload-online-url'),
  // 监听网络状态变化
  onNetworkStatusChanged: (callback: (isOnline: boolean) => void) => {
    ipcRenderer.on('network-status-changed', (_, isOnline) => callback(isOnline))
  },
  // 移除网络状态监听
  removeNetworkStatusListener: () => {
    ipcRenderer.removeAllListeners('network-status-changed')
  },
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}
