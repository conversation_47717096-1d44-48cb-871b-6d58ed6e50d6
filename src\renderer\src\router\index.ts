import { createRouter, createWebHashHistory } from 'vue-router'
import consumerRoutes from './consumer'
import maintenanceRoutes from './maintenance'
import { useAuthStore } from '../stores/auth'
import OfflineComponent from '../components/offline.vue'
import NetworkStatusDemo from '../views/NetworkStatusDemo.vue'

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      redirect: '/consumer/home',
    },
    {
      path: '/offline',
      name: 'Offline',
      component: OfflineComponent,
    },
    {
      path: '/network-demo',
      name: 'NetworkDemo',
      component: NetworkStatusDemo,
    },
    ...consumerRoutes,
    ...maintenanceRoutes,
    {
      path: '/:pathMatch(.*)*',
      redirect: '/consumer/home',
    },
  ],
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // 运维端路由需要认证
  // if (to.meta.requiresAuth && !authStore.isAuthenticated) {
  //   next({ name: 'MaintenanceLogin' })
  //   return
  // }

  // 正常放行
  next()
})

export default router
